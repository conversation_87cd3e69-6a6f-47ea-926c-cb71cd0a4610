<div class="mx-6">
    <article class="table-grid mt-5" *ngIf="showTableData">
        <div class="filter-list flex mb-5">
            <div>
                <div class="toggle-button">
                    <div>
                        <div class="input-container">
                            <input
                                gfkInput
                                [value]="inputValue"
                                (input)="onInputChange($event.target.value)"
                                placeholder="Look up by name, email, or more"
                                [disabled]="loadTableData"
                                eds-id="input-search-project"/>
                            <gfk-icon *ngIf="inputValue?.length > 0" [icon]="'close'" class="color-gfk-organge" (click)="closeClick()"></gfk-icon>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="userRole">
                <button
                    *ngIf="userRole?.name !== 'MarketData QC' && !isDeleted"
                    gfk-button
                    type="primary"
                    class="btn-secondary gfk-btn"
                    eds-id="add-qc-security-users-button"
                    [disabled]="loadTableData"
                    [disabled]="isDeleted"
                    (click)="openAddBpSecurityUsersModal()"
                    testId="add-qc-security-users-button">
                    Add Base Project Security Users
                </button>
            </div>
        </div>
        <eds-table eds-id="qc-period-table" hasRowSelect="checkbox" [isLoading]="loadTableData" (edsRowSelected)="onSelect($event, assignedBpUsersData)">
            <thead slot="thead">
                <eds-tr variant="header">
                    <eds-th variant="selector" [isSelected]=" areAllSelectedWithPagination(visibleAssignedBpUsersList)" [columnIndex]="0" *ngIf="userRole && userRole?.name !== 'MarketData QC'">
                        Username
                    </eds-th>
                    <eds-th variant="" [columnIndex]="0" *ngIf="!userRole || userRole?.name === 'MarketData QC'">
                        <span class="pl-5">Username</span>
                    </eds-th>
                    <eds-th column-index="2">Name</eds-th>
                    <eds-th column-index="3">Email</eds-th>
                    <eds-th column-index="4">Assigned By</eds-th>
                    <eds-th column-index="5">Assigned On</eds-th>
                </eds-tr>
            </thead>

            <tbody slot="tbody">
                <ng-container *ngIf="assignedBpUsersData?.length > 0; else noData">
                    <eds-tr *ngFor="let assignedUser of visibleAssignedBpUsersList; trackBy: trackById" [rowIndex]="assignedUser.id">
                        <eds-td [isSelected]="isSelected(assignedUser.id)" variant="selector" *ngIf="userRole && userRole?.name !== 'MarketData QC'">{{ assignedUser.userName }}</eds-td>
                        <eds-td variant="" *ngIf="!userRole || userRole?.name === 'MarketData QC'"><span class="pl-5">{{ assignedUser.userName }}</span></eds-td>
                        <eds-td>{{ assignedUser.fullName }}</eds-td>
                        <eds-td>{{ assignedUser.email }}</eds-td>
                        <eds-td>{{ assignedUser.assignedBy }}</eds-td>
                        <eds-td>{{ assignedUser.lastAssignedOn }}</eds-td>
                    </eds-tr>
                </ng-container>
                <ng-template #noData>
                    <eds-tr><eds-td colspan="6">No records found.</eds-td></eds-tr>
                </ng-template>
            </tbody>
        </eds-table>
        <gfk-pagination
            *ngIf="assignedBpUsersData?.length > 0 && assignedBpUsersData?.length > defaultPageSize"
            id="pagination-create-ld"
            [itemsPerPageOptions]="pageSizeOptions"
            [totalCount]="assignedBpUsersData?.length"
            [position]="'right'"
            [showItemsPerPage]="true"
            [showFirstAndLast]="true"
            [defaultPageSize]="defaultPageSize"
            [currentPage]="currentPageForAssignedBpUsers"
            (onPage)="onPageChangeAssignedBpUsers($event)">
        </gfk-pagination>
    </article>
    <div class="mt-5 flex items-center" *ngIf="!showTableData">
        <eds-icon
            eds-id="dx-qc-settings-info"
            icon="info_outline"
            color="default"
            size="md" >
        </eds-icon>
        <p class="pl-2 mb-0">There are no Assigned Users for this Base Project.</p>
    </div>
    <div class="mt-5 flex items-center cursor-pointer w-fit-content" (click)="openAddBpSecurityUsersModal()" *ngIf="!showTableData && userRole && userRole?.name !== 'MarketData QC' && !isDeleted">
        <div class="mr-2">
            <p class="mb-0 text-brand border-b-1">Add Base Project Security Users</p>
        </div>
        <eds-icon
            eds-id="dx-qc-settings-info"
            icon="add_outline"
            color="brand"
            size="md" >
        </eds-icon>
    </div>
</div>
<div *ngIf="hasSelection">
    <div class="h-32"></div>
    <div class="selected-qc-project-footer">
        <div>
            {{ selectedCount + ' Assigned Users Selected' }}
        </div>
        <div>
            <button
                *ngIf="userRole && !isDeleted"
                gfk-button
                type="primary"
                class="jira-button-margin-right transparent-background-theme-btn"
                (click)="openRemoveAssignedBpUsersModal()"
                [disabled]="isDeleted"
                eds-id="delete-qc-security-button"
                testId="delete-qc-security-button">
                Remove User(s)
            </button>
        </div>
    </div>
</div>

<!-- (onAction)="addBpSecurityUsers($event);"> -->

<gfk-modal
	[triggerModal]="addBpSecurityUsersModal"
	[modalTitle]="'Add Base Project Security Users'"
	cancelTitle="Cancel"
	[confirmTitle]="'Save'"
	[confirmDisabled]="disableModalBtn"
    (onAction)="closeAddBPSecurityUsersModal($event);">
	<form [formGroup]="addBpSecurityUsersForm" *ngIf="addBpSecurityUsersModal">
        <div class="mb-5">
            <gfk-radio-buttons [options]="radioBtnOptions" [isInline]="true" (onChange)="toggleFields($event)">
            </gfk-radio-buttons>
        </div>
        <div class="mb-5" *ngIf="showSourceField">
            <eds-select
                formControlName="sourceBPID"
                eds-id="sourceBPID"
                [options]="baseProjectList"
                [placeholder]="'Select the Source BP to copy users from'">
            </eds-select>
        </div>
        <dx-chip-autocomplete
            *ngIf="!showSourceField"
            class="filters-align"
            (val)="userkey($event)"
            formControlName="users"
            [options]="bpUserList"
            [id]="'user-input'"
            [placeholder]="'Type and select users from the list'"
            [label]="'Users'">
        </dx-chip-autocomplete>
        <div class="filters-align comma-separated-input-group" *ngIf="!showSourceField">
            <span>
                <input
                id="usernamesInput"
                gfkInput
                type="text"
                formControlName="commaSeparatedUsernames"
                class="gfk-text-input"
                placeholder="Provide comma seperated list of user-names"
                eds-id="comma-separated-usernames-input"/>   
            </span>             
        </div>
	</form>
</gfk-modal>

<gfk-modal
	[triggerModal]="removeAssignedBpUsersConfirmationModal"
	modalTitle="Confirm removal of Assigned Users"
	cancelTitle="Cancel"
	confirmTitle="Yes, delete"
	(onAction)="removeAssignedBpUsers($event);">
	<p>Selected users will be removed. Would you like to proceed?</p>
</gfk-modal>

<!-- Invalid Usernames Modal -->
<gfk-modal
	[triggerModal]="showInvalidUsernamesModal"
	modalTitle="Invalid Usernames"
	cancelTitle=""
	confirmTitle="Okay"
	(onAction)="closeInvalidUsernamesModal()">
	<div>
		<p>The following usernames could not be added as they are invalid:</p>
		<ul class="list-style ml-5">
			<li *ngFor="let username of invalidUsernames">
				<span>{{username}}</span>
			</li>
		</ul>
	</div>
</gfk-modal>

<!-- Delete Validation Modal -->
<gfk-modal
	[triggerModal]="deleteValidationModal"
	modalTitle="Users removed successfully"
	cancelTitle=""
	confirmTitle="Okay"
	(onAction)="closeDeleteValidationModal()">
	<div>
        <p class="flex">
            <span>
                Following users cannot be removed.
            </span>
            <span [gfk-tooltip]="'(Master level user cannot be removed manually)'" tooltipDirection="right" tooltipVariant="dark" class="ml-2">
                <eds-icon
                    eds-id="dx-bp-security-delete-info"
                    icon="info_outline"
                    color="default"
                    size="md" >
                </eds-icon>
            </span>
        </p>
        <ul class="list-style ml-5">
            <li *ngFor="let conflictUser of deleteConflictUser">
                <span>{{conflictUser?.fullName + ' - (' + conflictUser?.email + ')'}}</span>
            </li>
        </ul>
	</div>
</gfk-modal>

