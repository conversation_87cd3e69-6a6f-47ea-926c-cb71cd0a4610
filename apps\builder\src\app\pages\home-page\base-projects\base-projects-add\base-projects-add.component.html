<div class="empty-screen-height" *ngIf="hideForm">
	<div class="empty-screen-alert">
		<div>
			<h3>
				<eds-icon
					eds-id="dx-bp-lock"
					icon="lock"
					size="xl"
					color="inactive"
					hovercolor="brand"
					class="hydrated">
				</eds-icon>
				<br><b>Insufficient Privileges!</b><br><br>You do not have permission to access this information. Please request for respective user role from settings.
			</h3>
		</div>
	</div>
</div>
<div *ngIf="!hideForm">
    <div class="heading p-1.5">
        <div class="mx-6">
            <eds-breadcrumbs>
                <a routerLink="/base-projects/list" queryParamsHandling="preserve">Base and QC Projects</a>
                <a routerLink="/base-projects/create" queryParamsHandling="preserve">{{(baseProjectID && editBaseProject) ? ('BP ID # ' + baseProjectID + (data?.qcProjects ? (' QC ID # ' + (data?.qcProjects?.id)) : '')) : 'Create a new Base and QC Project'}}</a>
            </eds-breadcrumbs>
        </div>
        <div class="mx-6 flex items-center gap-2 justify-between">
            <h3 class="mb-0 font-bold">{{baseProjectID ? addBaseProjectFormGroup.get('baseProjectName')?.value : 'New Base and QC Project [Unnamed]'}}</h3>
            <p class="m-0 last-update-time-text flex items-center" *ngIf="editBaseProject">
                <span [gfk-tooltip]="createdOn" tooltipDirection="top" tooltipVariant="dark" class="mr-2" *ngIf="lastUpdateOn">
                    <eds-icon
                        eds-id="dx-bp-info"
                        size="md"
                        icon="info_outline"
                        color="inactive">
                    </eds-icon>
                </span>
                <span class="pb-1">
                    {{lastUpdateOn ? lastUpdateOn : createdOn}}
                </span>
            </p>
        </div>
    </div>

    <div *ngIf="showBCRConflictsTab">
      <div class="heading p-1.5 flex justify-between navigation-tabs" id="navigation-tabs">
          <div class="flex items-center">
              <div
                  class="mx-7 tabs-item" [ngClass]="selectedTab === 'createBaseProject' ? 'active' : ''"  (click)="tabChange('bpSettings')">
                  <p class="mb-0">Create Base Project</p>
              </div>
              <div
                  class="tabs-item mx-7" *ngIf="showBCRConflictsTab" [ngClass]="selectedTab === 'bcrConflicts' ? 'active' : ''" (click)="tabChange('bcrConflicts')">
                  <p class="mb-0">BCR Conflicts</p>
              </div>
          </div>
      </div>
    </div>

    <div class="heading p-1.5 flex justify-between navigation-tabs" id="navigation-tabs" *ngIf="baseProjectID">
        <div class="flex items-center">
            <div class="mr-7 tabs-item" [ngClass]="selectedTab === 'bpSettings' ? 'active' : ''" (click)="tabChange('bpSettings')">
                <p class="mb-0">Base Project Settings</p>
            </div>
            <div class="mr-7 tabs-item" [ngClass]="selectedTab === 'bpSecurity' ? 'active' : ''" (click)="tabChange('bpSecurity')">
              <p class="mb-0">Base Project Security</p>
          </div>
            <div class="mr-7 tabs-item" [ngClass]="selectedTab === 'qcProjectSettings' ? 'active' : ''" (click)="tabChange('qcProjectSettings')" *ngIf="!hideQCTabs">
                <p class="mb-0">QC Project Settings</p>
            </div>

            <div class="mr-7 tabs-item" [ngClass]="selectedTab === 'qcPeriods' ? 'active' : ''" (click)="tabChange('qcPeriods')" *ngIf="!hideQCTabs && !copyBaseProject">
                <p class="mb-0">QC Periods</p>
            </div>
            <div class="mr-7 tabs-item" [ngClass]="selectedTab === 'qcSecurity' ? 'active' : ''" (click)="tabChange('qcSecurity')" *ngIf="!hideQCTabs && !copyBaseProject">
                <p class="mb-0">QC Security</p>
            </div>
            <div class="mr-7 tabs-item" [ngClass]="selectedTab === 'bpAssociations' ? 'active' : ''" (click)="tabChange('bpAssociations')" *ngIf="!hideQCTabs && !copyBaseProject">
                <p class="mb-0">Base Project Associations</p>
            </div>
            <div
                class="tabs-item mx-7" *ngIf="showBCRConflictsTabEdit" [ngClass]="selectedTab === 'bcrConflicts' ? 'active' : ''" (click)="tabChange('bcrConflicts')">
                <p class="mb-0">BCR Conflicts</p>
            </div>
        </div>
        <div>
           <button *ngIf="editBaseProject && userRole?.name === 'Master' && isDeleted"
            gfk-button
            type="secondary"
            class="transparent-background-Restore-btn"
            eds-id="restore-base-project"
            testId="restore-base-project"
            (click)="openRestoreConfirmationModal()">

                <eds-icon
                size="sm"
                icon="refresh"
                class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
                color="brand"
            ></eds-icon>
            Restore Base Project
            </button>
            <button
                *ngIf="(userRole?.name === 'Master' || userRole?.name === 'Account Manager' || userRole?.name === 'MarketData QC') && isTypeIdValid && !copyBaseProject && !isDeleted"
                gfk-button
                [type]="isIRSeparationRequested ? 'secondary' : 'primary'"
                [ngClass]="{
                    'transparent-background-IR-btn': isIRSeparationRequested,
                    'transparent-background-IRNotRequested-btn': !isIRSeparationRequested
                }"
                [disabled]="isIRSeparationRequested"
                eds-id="request-ir-separation"
                testId="request-ir-separation"
                (click)="!isIRSeparationRequested && navigateToIRSeparationAdd()">
                <eds-icon
                    size="sm"
                    [icon]="isIRSeparationRequested ? 'check_outline' : 'fractional'"
                    class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
                    [color]="isIRSeparationRequested ? 'success' : 'brand'">
                </eds-icon>
                {{ isIRSeparationRequested ? 'I/R Separation Requested' : 'Request I/R Separation' }}
            </button>
            <button *ngIf="editBaseProject && userRole?.name === 'Master' && !isDeleted" 
                gfk-button
                type="primary"
                eds-id="create-base-project-button"
                testId="create-base-project-button"
                class="btn-secondary gfk-btn"
                [ngClass]="baseProjectID ? 'ml-6' : ''"
                (click)="navigationToCopyBaseProject()">
                <eds-icon
                    size="sm"
                    icon="copy"
                    class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
                    color="white">
                </eds-icon>
                Copy Base Project Settings
            </button>
        </div>

    </div>
    <div [hidden]="selectedTab !== 'bpSettings'" >
        <form [formGroup]="addBaseProjectFormGroup" (ngSubmit)="saveBaseProject()" class="mt-4 mx-20">
            <div class="w-2/5">
                <gfk-radio-buttons
                    formControlName="baseProjectPanel"
                    [options]="baseProjectPanelOptions"
                    [labelGroup]="baseProjectPanelLabelGroup"
                    name="baseProjectPanel"
                    [isInline]="true"
                    [disabled]="editBaseProject ? true : false"
                    (onChange)="panelChangeEvent($event, true)"
                    eds-id="baseProjectPanel">
                </gfk-radio-buttons>
                <div class="mt-5" *ngIf="!hideFieldsForDistributor">
                    <gfk-radio-buttons
                        formControlName="baseProjectType"
                        [options]="baseProjectTypeOptions"
                        [labelGroup]="baseProjectTypeLabelGroup"
                        name="baseProjectType"
                        [isInline]="true"
                        [disabled]="editBaseProject ? true : false"
                        eds-id="baseProjectType">
                    </gfk-radio-buttons>
                </div>
                <div class="mt-5">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Data Type
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <eds-select
                        formControlName="baseProjectDataType"
                        eds-id="baseProjectDataType"
                        (edsChange)="(baseProjectID && !copyBaseProject) ? '' : resetAutoGenerateName()"
                        [options]="(filteredDataTypeData.length) ? filteredDataTypeData : dataTypeData"
                        [required]="true"
                        [placeholder]="'Start typing to see the data type'"
                        [value]="addBaseProjectFormGroup.get('baseProjectDataType')?.value">
                    </eds-select>
                    <p class="error-text" *ngIf="showDistributorPanelError && (userRole?.name === 'Master')">Distributor panel is limited to using the Leader Data Types only.</p>
                    <p class="error-text" *ngIf="showDataTypeError && (userRole?.name === 'Master')">This value is not valid for the selected Periodicity.</p>
                </div>
                <div class="mt-5">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Purpose
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <eds-select
                        formControlName="baseProjectPurpose"
                        eds-id="baseProjectPurpose"
                        [options]="purposeData"
                        [required]="true"
                        [placeholder]="'Start typing to see the purpose'"
                        [value]="addBaseProjectFormGroup.get('baseProjectPurpose')?.value">
                    </eds-select>
                </div>
                <div class="mt-5">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Periodicity
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <eds-select
                        formControlName="baseProjectPeriodicity"
                        eds-id="baseProjectPeriodicity"
                        [options]="periodicityData"
                        [required]="true"
                        [disabled]="editBaseProject ? true : (disableField || hideFieldsForDistributor)"
                        [placeholder]="'Start typing to see the periodicities'"
                        [value]="addBaseProjectFormGroup.get('baseProjectPeriodicity')?.value">
                    </eds-select>
                    <p class="error-text" *ngIf="showPeriodicityError && (userRole?.name === 'Master')">Weekly Channel is not allowed as periodicity for Monthly Extrapolated data type.</p>
                </div>
                <div class="mt-5">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Country
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <eds-select
                        formControlName="baseProjectCountry"
                        eds-id="baseProjectCountry"
                        (edsChange)="resetAutoGenerateName()"
                        [options]="countriesList"
                        [required]="true"
                        [disabled]="editBaseProject ? true : disableField"
                        [placeholder]="'Start typing to see the list of countries'"
                        [value]="addBaseProjectFormGroup.get('baseProjectCountry')?.value">
                    </eds-select>
                </div>
                <div class="mt-5">
                    <div class="mb-2" *ngIf="userRole?.name === 'Master'">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Assign a Product Group
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <eds-select
                        *ngIf="userRole?.name === 'Master'"
                        formControlName="baseProjectProductGroup"
                        eds-id="baseProjectProductGroup"
                        (edsChange)="resetAutoGenerateName()"
                        [options]="productGroupData"
                        [required]="true"
                        [multiple]="true"
                        [placeholder]="'Search by Product Group Name or ID'"
                        [value]="addBaseProjectFormGroup.get('baseProjectProductGroup')?.value">
                    </eds-select>
                    <div *ngIf="userRole?.name !== 'Master' && selectedProductGroupLabels?.length" class="selected-base-project">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">Selected Product Group</label>
                        <div class="gfk-chip-container">
                            <gfk-chip *ngFor="let productGroup of selectedProductGroupLabels"><span>{{productGroup}}</span></gfk-chip>
                        </div>
                    </div>
                </div>
                <div class="mt-5" *ngIf="!hideFieldsForDistributor">
                    <div class="mb-2" *ngIf="userRole?.name === 'Master'">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Assign a predecessor Base Project?
                        </label>
                    </div>
                    <eds-select
                        *ngIf="userRole?.name === 'Master'"
                        formControlName="baseProjectPredecessor"
                        eds-id="baseProjectPredecessor"
                        [options]="baseProjectData"
                        [required]="false"
                        [multiple]="true"
                        [placeholder]="'Search by BaseProject ID or BaseProject name'"
                        [disabled]="disableBPEdit ? true : false"
                        [value]="addBaseProjectFormGroup.get('baseProjectPredecessor')?.value">
                    </eds-select>
                    <div *ngIf="userRole?.name !== 'Master' && selectedPredecessorBaseProjectLabels?.length" class="selected-base-project">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">Selected Predecessor Base Project</label>
                        <div class="gfk-chip-container">
                            <gfk-chip *ngFor="let predecessorBP of selectedPredecessorBaseProjectLabels"><span>{{predecessorBP}}</span></gfk-chip>
                        </div>
                        <div class="gfk-chip-container mb-2" *ngIf="!selectedPredecessorBaseProjectLabels?.length">
                            <p>No Predecessor Base Project Available.</p>
                        </div>
                    </div>
                </div>
                <div class="mt-6 mb-5">
                    <p class="font-bold">Base Project name*</p>
                    <div class="flex justify-between">

                        <button
                            gfk-button
                            type="button"
                            class="w-2/3 mr-2"
                            [ngClass]="loadTableData ? '' : 'transparent-background-theme-btn'"
                            eds-id="auto-generate-name-button"
                            testId="auto-generate-name-button"
                            (click)="generateBaseProjectName()"
                            [disabled]="loadTableData"
                            *ngIf="userRole?.name === 'Master' && (disableAutoGenerateName || !firsttime) || (copyBaseProject && disableAutoGenerateName)">
                            Auto-generate name
                        </button>
                        <input
                            formControlName="baseProjectName"
                            gfk-input
                            type="text"
                            name="baseProjectName"
                            [placeholder]="'Auto-generate name'"
                            [value]="addBaseProjectFormGroup.get('baseProjectName')?.value"
                            eds-id="baseProjectName"
                            *ngIf="(!disableAutoGenerateName && firsttime) || (copyBaseProject && !disableAutoGenerateName)"
                            class="mr-2 w-2/3" />
                        <input
                            formControlName="suffixes"
                            gfk-input
                            type="text"
                            name="suffixes"
                            [placeholder]="'Suffixes (optional)'"
                            [value]="addBaseProjectFormGroup.get('suffixes')?.value"
                            [disabled]="disableBPEdit ? true : false"
                            eds-id="suffixes"
                            class="mr-2 w-1/3" />
                    </div>
                    <p class="error-text" *ngIf="addBaseProjectFormGroup.get('combinedName')?.value?.length > 40">Base project names are max 40 characters</p>
                    <p class="error-text" *ngIf="showError">Please select the required fields to generate base project name.</p>
                </div>
                <div class="mt-5 flex items-center" *ngIf="!baseProjectID && addBaseProjectFormGroup.get('baseProjectPanel')?.value !== '3'">
                    <eds-icon
                        eds-id="dx-qc-settings-info"
                        icon="info_outline"
                        color="default"
                        size="md" >
                    </eds-icon>
                    <p class="pl-2 mb-0">The QC Project will be created automatically with the default QC Settings</p>
                </div>
            </div>
            <div class="flex w-3/6" [ngClass]="baseProjectID ? 'mt-10' : 'mt-5'" 
            *ngIf="userRole?.name === 'Master'">
                <button
                    gfk-button
                    type="button"
                    [ngClass]="loadTableData ? '' : 'transparent-background-theme-btn'"
                    eds-id="reset-btn"
                    testId="reset-btn"
                    (click)="displayResetFormModal()"
                    [disabled]="loadTableData">
                    Reset
                </button>
                <button
                    gfk-button
                    type="button"
                    eds-id="delete-base-project-button"
                    testId="delete-base-project-button"
                    class="btn-secondary gfk-btn ml-6"
                    [ngClass]="loadTableData ? '' : 'transparent-background-delete-btn'"
                    (click)="openDeleteConfirmationModal()"
                    [disabled]="loadTableData"
                    *ngIf="baseProjectID && editBaseProject && !isDeleted">
                    <eds-icon
                        size="sm"
                        [icon]="icon"
                        class="icon flex items-center justify-center transition-colors fill-brand stroke-brand h-5 w-5 hydrated"
                        [color]="loadTableData ? 'inactive' : 'brand'">
                    </eds-icon>
                    Delete Base Project
                </button>
                <button
                    gfk-button
                    type="primary"
                    [disabled]=" isDeleted || !addBaseProjectFormGroup.get('baseProjectName')?.value.trim() || addBaseProjectFormGroup?.invalid || showPeriodicityError || showDistributorPanelError || showDataTypeError || disableAutoGenerateName || loadTableData"
                    eds-id="create-base-project-button"
                    testId="create-base-project-button"
                    class="btn-secondary gfk-btn"
                    [ngClass]="copyBaseProject ? '' : 'ml-6'">
                    {{(baseProjectID && editBaseProject) ? 'Save Base Project Settings' : 'Create Base Project'}}
                </button>
            </div>
        </form>
    </div>
    <div [hidden]="selectedTab !== 'bcrConflicts'">
        <!-- BCR Conflicts Table -->
        <div class="mx-6">
            <article class="table-grid mt-5">
                <eds-table eds-id="bcr-conflicts-table" [isLoading]="loadTableData">
                    <thead slot="thead">
                        <eds-tr variant="header">
                            <eds-th column-index="1">BCR ID</eds-th>
                            <eds-th column-index="2">Product Group</eds-th>
                            <eds-th column-index="3">Source Channel</eds-th>
                            <eds-th column-index="4">Feature / Feature Value</eds-th>
                            <eds-th column-index="5">Outlet ID</eds-th>
                            <eds-th column-index="6">Target Channel</eds-th>
                            <eds-th column-index="7">From Period</eds-th>
                            <eds-th column-index="8">To Period</eds-th>
                            <eds-th column-index="9">Message</eds-th>
                        </eds-tr>
                    </thead>
                    <tbody slot="tbody">
                        <eds-tr
                            *ngFor="let bcrDetail of visibleBCRDetailList; trackBy: trackById"
                            [rowIndex]="bcrDetail?.baseChannelRearrangementId">
                            <eds-td>{{ bcrDetail?.baseChannelRearrangementId }}</eds-td>
                            <eds-td>{{ bcrDetail?.productGroup }}</eds-td>
                            <eds-td>{{ bcrDetail?.channel }}</eds-td>
                            <eds-td>{{ bcrDetail?.feature }} / {{ bcrDetail?.featureValue }}</eds-td>
                            <eds-td>{{ bcrDetail?.outletId }}</eds-td>
                            <eds-td>{{ bcrDetail?.baseChannel }}</eds-td>
                            <eds-td>{{ bcrDetail?.fromPeriodId }}</eds-td>
                            <eds-td>{{ bcrDetail?.toPeriodId }}</eds-td>
                            <eds-td>{{ bcrDetail?.message }}</eds-td>
                        </eds-tr>
                    </tbody>
                </eds-table>
                <gfk-pagination
                    *ngIf="bcrDetailList?.length > 0 && bcrDetailList?.length > defaultPageSizeforBCRDetail"
                    id="pagination-create-ld"
                    [itemsPerPageOptions]="pageSizeOptionsForBCRDetail"
                    [totalCount]="bcrDetailList?.length"
                    [position]="'right'"
                    [showItemsPerPage]="true"
                    [showFirstAndLast]="true"
                    [defaultPageSize]="defaultPageSizeforBCRDetail"
                    [currentPage]="currentPageForBCRDetail"
                    (onPage)="onPageChangeBCRDetail($event)">
                </gfk-pagination>
            </article>
        </div>
        <div class="flex justify-end mx-6 mt-4">
            <button
                gfk-button
                type="primary"
                class="btn-secondary gfk-btn"
                (click)="copyBcrConflictsLink()"
                eds-id="copy-bcr-conflicts-button">
                Copy to Clipboard
            </button>
        </div>
    </div>

    <div [hidden]="selectedTab !== 'bpSecurity'">
       <dx-base-projects-security [baseProjectList]="baseProjectListForQCPeriod" [baseProjectId]="baseProjectID" [userRole]="userRole" [bpUserList]="userList" [isDeleted]="data?.deleted" (getProjectAssignedBpUsers)="getProjectAssignedBpUsers()" ></dx-base-projects-security>
    </div>

    <div [hidden]="selectedTab !== 'qcProjectSettings'">
        <form [formGroup]="qcProjectSettingsFormGroup" (ngSubmit)="updateQCProjectSettings()" class="mt-4 mx-20 w-2/5">
            <div *ngIf="showSQCFields">
                <div class="mb-4">
                    <gfk-radio-buttons
                        formControlName="sqcMode"
                        [options]="sqcModeOptions"
                        [labelGroup]="sqcModeLabelGroup"
                        name="sqcMode"
                        [isInline]="true"
                        [disabled]="(disableQCProjectEdit || disableQCProjectEditField) ? true : false"
                        eds-id="sqcMode">
                    </gfk-radio-buttons>
                </div>
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        Auto-price Check
                    </label>
                </div>
                <div class="flex items-center">
                    <eds-checkbox
                        edsId="isAutomatedPriceCheck"
                        [disabled]="(disableQCProjectEdit || disableQCProjectEditField) ? true : false"
                        hasError="false"
                        indeterminate="false"
                        [checked]="isAutomatedPriceCheck"
                        name="isAutomatedPriceCheck"
                        (edsChange)="qcProjectSettingInput($event, 'isAutomatedPriceCheck'); unsavedQCChangesValue(qcProjectSettingsFormGroup.value)">
                    </eds-checkbox>
                    <span class="ml-4">Enable</span>
                </div>
            </div>
            <div class="mt-5" *ngIf="showAutoLoadFields">
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        Auto load data
                    </label>
                </div>
                <div class="flex items-center">
                    <eds-checkbox
                        edsId="isAutoLoad"
                        [disabled]="disableQCProjectEdit ? true : false"
                        hasError="false"
                        indeterminate="false"
                        [checked]="isAutoLoad"
                        name="isAutoLoad"
                        (edsChange)="qcProjectSettingInput($event, 'isAutoLoad'); unsavedQCChangesValue(qcProjectSettingsFormGroup.value)">
                    </eds-checkbox>
                    <span class="ml-4">Enable</span>
                </div>
            </div>
            <div class="mt-5" *ngIf="showAutoLoadFields">
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        Reset data corrections
                    </label>
                </div>
                <eds-select
                    formControlName="resetCorrectionTypeId"
                    eds-id="resetCorrectionTypeId"
                    [options]="resetDataCorrectionTypeList"
                    [required]="false"
                    [disabled]="disableQCProjectEdit ? true : false"
                    [placeholder]="'Start typing to see the Reset Correction Type List'"
                    [value]="qcProjectSettingsFormGroup.get('resetCorrectionTypeId')?.value">
                </eds-select>
            </div>
            <div class="mt-10" *ngIf="(showSQCFields || showAutoLoadFields) && !copyBaseProject && userRole && !isDeleted">
                <button
                    gfk-button
                    type="button"
                    [ngClass]="loadTableData ? '' : 'transparent-background-theme-btn'"
                    eds-id="reset-btn"
                    testId="reset-btn"
                    (click)="displayResetFormModal()"
                    *ngIf="!copyBaseProject"
                    [disabled]="loadTableData">
                    Reset
                </button>
                <button
                    gfk-button
                    type="primary"
                    [disabled]="qcProjectSettingsFormGroup?.invalid"
                    eds-id="save-qc-projects-settings"
                    testId="save-qc-projects-settings"
                    [disabled]="loadTableData"
                    class="btn-secondary gfk-btn">
                    Save QC Project Settings
                </button>
            </div>
        </form>
        <div class="mt-5 mx-6 flex items-center" *ngIf="!showSQCFields && !showAutoLoadFields">
            <eds-icon
                eds-id="dx-qc-settings-alert"
                icon="error"
                color="default"
                size="md">
            </eds-icon>
            <p class="pl-2 mb-0">No data available for display.</p>
        </div>
    </div>
    <div [hidden]="selectedTab !== 'qcPeriods'">
        <div class="mx-6">
            <article class="table-grid mt-5" *ngIf="showQCPeriodTable">
                <div class="text-right mb-5" *ngIf="userRole && !isDeleted">
                    <button
                        gfk-button
                        type="primary"
                        class="btn-secondary gfk-btn"
                        eds-id="add-qc-period-button"
                        (click)="openQCPeriodFormModal()"
                        [disabled]="!qcPeriodData?.length || loadQCPeriodTableData"
                        testId="add-qc-period-button">
                        Add a QC Period
                    </button>
                </div>
                <eds-table eds-id="qc-period-table" hasRowSelect="checkbox" [isLoading]="loadQCPeriodTableData" (edsRowSelected)="onSelect($event, qcPeriodData)">
                    <thead slot="thead">
                        <eds-tr variant="header">
                            <eds-th variant="selector" [isSelected]="areAllSelectedWithPagination(visibleQCPeriods)" [columnIndex]="0" *ngIf="userRole">
                                Period
                            </eds-th>
                            <eds-th variant="" [isSelected]="areAllSelectedWithPagination(visibleQCPeriods)" [columnIndex]="0" *ngIf="!userRole">
                                Period
                            </eds-th>
                            <eds-th column-index="1">Reference Period 1</eds-th>
                            <eds-th column-index="2">Reference Period 2</eds-th>
                            <eds-th column-index="3">Reference Period 3</eds-th>
                            <eds-th column-index="4">Reference Period 4</eds-th>
                            <eds-th column-index="5">Reference Period 5</eds-th>
                            <eds-th column-index="6">Reference Period 6</eds-th>
                            <eds-th column-index="7">Reference Period 7</eds-th>
                        </eds-tr>
                    </thead>
                
                    <tbody slot="tbody">
                        <ng-container *ngIf="visibleQCPeriods?.length > 0; else noData">
                            <eds-tr *ngFor="let qcPeriod of visibleQCPeriods; trackBy: trackById" [rowIndex]="qcPeriod.id">
                                <eds-td [isSelected]="isSelected(qcPeriod.id)" variant="selector" *ngIf="userRole">
                                    <span class="bp-id-style cursor-pointer" (click)="getQCPeriodByQCPeriodId(qcPeriod.id)">
                                        {{ qcPeriod.periodName ? splitPeriodName(qcPeriod.periodName) : 'N/A' }}
                                    </span>
                                </eds-td>
                                <eds-td [isSelected]="isSelected(qcPeriod.id)" variant="" *ngIf="!userRole && !loadTableData">
                                    <span class="bp-id-style cursor-pointer" (click)="getQCPeriodByQCPeriodId(qcPeriod.id)">
                                        {{ qcPeriod.periodName ? splitPeriodName(qcPeriod.periodName) : 'N/A' }}
                                    </span>
                                </eds-td>        
                                <eds-td>{{ qcPeriod.refPeriod1 ? splitPeriodName(qcPeriod.refPeriod1) : 'N/A' }}</eds-td>
                                <eds-td>{{ qcPeriod.refPeriod2 ? splitPeriodName(qcPeriod.refPeriod2) : 'N/A' }}</eds-td>
                                <eds-td>{{ qcPeriod.refPeriod3 ? splitPeriodName(qcPeriod.refPeriod3) : 'N/A' }}</eds-td>
                                <eds-td>{{ qcPeriod.refPeriod4 ? splitPeriodName(qcPeriod.refPeriod4) : 'N/A' }}</eds-td>
                                <eds-td>{{ qcPeriod.refPeriod5 ? splitPeriodName(qcPeriod.refPeriod5) : 'N/A' }}</eds-td>
                                <eds-td>{{ qcPeriod.refPeriod6 ? splitPeriodName(qcPeriod.refPeriod6) : 'N/A' }}</eds-td>
                                <eds-td>{{ qcPeriod.refPeriod7 ? splitPeriodName(qcPeriod.refPeriod7) : 'N/A' }}</eds-td>
                            </eds-tr>
                        </ng-container>               
                        <ng-template #noData>
                            <eds-tr>
                                <eds-td colspan="8">No records found.</eds-td>
                            </eds-tr>
                        </ng-template>
                    </tbody>
                </eds-table>               
                <gfk-pagination
                    *ngIf="qcPeriodData?.length > 0 && qcPeriodData?.length > defaultPageSize"
                    id="pagination-create-ld"
                    [itemsPerPageOptions]="pageSizeOptions"
                    [totalCount]="qcPeriodData?.length"
                    [position]="'right'"
                    [showItemsPerPage]="true"
                    [showFirstAndLast]="true"
                    [defaultPageSize]="defaultPageSize"
                    [currentPage]="currentPage"
                    (onPage)="onPageChangeQCPeriods($event)">
                </gfk-pagination>
            </article>
            <div class="mt-5 flex items-center" *ngIf="!showQCPeriodTable">
                <eds-icon
                    eds-id="dx-qc-settings-info"
                    icon="info_outline"
                    color="default"
                    size="md" >
                </eds-icon>
                <p class="pl-2 mb-0">There are no QC Periods for this QC Project.</p>
            </div>
            <div class="mt-5 flex items-center cursor-pointer w-fit-content" (click)="openQCPeriodFormModal()" *ngIf="!showQCPeriodTable">
                <div class="mr-2">
                    <p class="mb-0 text-brand border-b-1">Add a new QC Period</p>
                </div>
                <eds-icon
                    eds-id="dx-qc-settings-info"
                    icon="add_outline"
                    color="brand"
                    size="md" >
                </eds-icon>
            </div>
        </div>
        <div *ngIf="hasSelection">
            <div class="h-32"></div>
            <div class="selected-qc-project-footer">
                <div>
                    {{ selectedCount + ' QC Period Selected' }}
                </div>
                <div>
                    <button
                        *ngIf="userRole && !isDeleted"
                        gfk-button
                        type="secondary"
                        class="jira-button-margin-right"
                        [ngClass]="selectedCount > 1 ? '' : 'transparent-background-theme-btn'"
                        (click)="openCreateBulkQCPeriodModal()"
                        eds-id="bulk-qc-project-button"
                        testId="bulk-qc-project-button"
                        [disabled]="selectedCount > 1">
                        Bulk Create QC Period
                    </button>
                    <button
                        *ngIf="userRole && !isDeleted"
                        gfk-button
                        type="primary"
                        class="jira-button-margin-right transparent-background-theme-btn"
                        (click)="openDeleteQCPeriodConfirmationModal()"
                        eds-id="delete-qc-project-button"
                        testId="delete-qc-project-button">
                        Delete QC Period(s)
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div [hidden]="selectedTab !== 'qcSecurity'">
        <div class="mx-6">
            <article class="table-grid mt-5" *ngIf="showQCSecurityTable">
                <div class="filter-list flex mb-5">
                    <div>
                        <div class="toggle-button">
                            <div>
                                <div class="input-container">
                                    <input
                                        gfkInput
                                        [value]="inputValue"
                                        (input)="onInputChange($event.target.value)"
                                        placeholder="Look up by name, email, or more"
                                        [disabled]="loadQCSecurityTableData"
                                        eds-id="input-search-project"/>
                                    <gfk-icon *ngIf="inputValue?.length > 0" [icon]="'close'" class="color-gfk-organge" (click)="closeClick()"></gfk-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-right" *ngIf="userRole">
                        <button
                            *ngIf="userRole?.name !== 'MarketData QC' && !isDeleted"
                            gfk-button
                            type="primary"
                            class="btn-secondary gfk-btn"
                            eds-id="add-qc-security-users-button"
                            [disabled]="loadQCSecurityTableData"
                            [disabled]="isDeleted"
                            (click)="openAddQCSecurityUsersModal()"
                            testId="add-qc-security-users-button">
                            Add QC Security Users
                        </button>
                    </div>
                </div>
                <eds-table eds-id="qc-period-table" hasRowSelect="checkbox" [isLoading]="loadQCSecurityTableData" (edsRowSelected)="onSelect($event, assignedUsersData)">
                    <thead slot="thead">
                        <eds-tr variant="header">
                            <eds-th variant="selector" [isSelected]="areAllSelectedWithPagination(visibleAssignedUsersList)" [columnIndex]="0" *ngIf="userRole && userRole?.name !== 'MarketData QC'">
                                Username
                            </eds-th>
                            <eds-th variant="" [columnIndex]="0" *ngIf="!userRole || userRole?.name === 'MarketData QC'">
                                <span class="pl-5">Username</span>
                            </eds-th>
                            <eds-th column-index="2">Name</eds-th>
                            <eds-th column-index="3">Email</eds-th>
                            <eds-th column-index="4">Assigned By</eds-th>
                            <eds-th column-index="5">Assigned On</eds-th>
                        </eds-tr>
                    </thead>

                    <tbody slot="tbody">
                        <ng-container *ngIf="visibleAssignedUsersList?.length > 0; else noData">
                            <eds-tr *ngFor="let assignedUser of visibleAssignedUsersList; trackBy: trackById" [rowIndex]="assignedUser.id">
                                <eds-td [isSelected]="isSelected(assignedUser.id)" variant="selector" *ngIf="userRole && userRole?.name !== 'MarketData QC'">{{ assignedUser.userName }}</eds-td>
                                <eds-td variant="" *ngIf="!userRole || userRole?.name === 'MarketData QC'"><span class="pl-5">{{ assignedUser.userName }}</span></eds-td>
                                <eds-td>{{ assignedUser.fullName }}</eds-td>
                                <eds-td>{{ assignedUser.email }}</eds-td>
                                <eds-td>{{ assignedUser.assignedBy }}</eds-td>
                                <eds-td>{{ assignedUser.lastAssignedOn }}</eds-td>
                            </eds-tr>
                        </ng-container>
                        <ng-template #noData>
                            <eds-tr><eds-td colspan="6">No records found.</eds-td></eds-tr>
                        </ng-template>
                    </tbody>
                </eds-table>
                <gfk-pagination
                    *ngIf="assignedUsersData?.length > 0 && assignedUsersData?.length > defaultPageSize"
                    id="pagination-create-ld"
                    [itemsPerPageOptions]="pageSizeOptions"
                    [totalCount]="assignedUsersData?.length"
                    [position]="'right'"
                    [showItemsPerPage]="true"
                    [showFirstAndLast]="true"
                    [defaultPageSize]="defaultPageSize"
                    [currentPage]="currentPageForAssignedUsers"
                    (onPage)="onPageChangeAssignedUsers($event)">
                </gfk-pagination>
            </article>
            <div class="mt-5 flex items-center" *ngIf="!showQCSecurityTable">
                <eds-icon
                    eds-id="dx-qc-settings-info"
                    icon="info_outline"
                    color="default"
                    size="md" >
                </eds-icon>
                <p class="pl-2 mb-0">There are no Assigned Users for this QC Project.</p>
            </div>
            <div class="mt-5 flex items-center cursor-pointer w-fit-content" (click)="openAddQCSecurityUsersModal()" *ngIf="!showQCSecurityTable && userRole && userRole?.name !== 'MarketData QC' && !isDeleted">
                <div class="mr-2">
                    <p class="mb-0 text-brand border-b-1">Add QC Security Users</p>
                </div>
                <eds-icon
                    eds-id="dx-qc-settings-info"
                    icon="add_outline"
                    color="brand"
                    size="md" >
                </eds-icon>
            </div>
        </div>
        <div *ngIf="hasSelection">
            <div class="h-32"></div>
            <div class="selected-qc-project-footer">
                <div>
                    {{ selectedCount + ' Assigned Users Selected' }}
                </div>
                <div>
                    <button
                        *ngIf="userRole && !isDeleted"
                        gfk-button
                        type="primary"
                        class="jira-button-margin-right transparent-background-theme-btn"
                        (click)="openRemoveAssignedUsersModal()"
                        eds-id="delete-qc-security-button"
                        testId="delete-qc-security-button">
                        Remove User(s)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- The Modal -->
<gfk-modal
	[triggerModal]="deleteConfirmationModal"
	modalTitle="Confirm deletion of Base Project"
	cancelTitle="Cancel"
	confirmTitle="Yes, delete"
	(onAction)="deleteBaseProject($event);">
	<p>This Base project will be deleted. Links to predecessor projects or production projects will be removed. Would you like to proceed?</p>
</gfk-modal>

<!-- Delete Validation Modal -->
<gfk-modal
	[triggerModal]="deleteValidationModal"
	modalTitle="Delete Base Projects Results"
	cancelTitle=""
	confirmTitle="Okay"
	(onAction)="closeDeleteValidationModal()">
	<div>
		<p *ngIf="displayDeleteWithErrorMessage">
			Following BaseProject(s) deleted successfully: <br>
			<span class="text-blue-color">{{deletedBps.toString().replaceAll(',', ', ')}}</span>
		</p>
		<p>
			Following BaseProject(s) cannot be deleted due to dependencies or linkages to other projects:
		</p>
		<div class="bp-conflict-detail-height">
			<div *ngFor="let bpConflict of bpDeleteConflictDetails" class="bp-conflict-detail-card">
				<p class="flex mb-0">
					<span class="mr-2">
						<eds-icon
							eds-id="dx-qc-settings-info"
							icon="info"
							color="inactive"
							size="sm" >
						</eds-icon>
					</span>
					<span class="text-lighter">
						BP ID: {{bpConflict?.baseProjectId}}
					</span>
				</p>
				<p *ngIf="bpConflict?.dependency?.productionProjectIds?.length">
					<span class="text-brand">ProductionProject: </span>
					<span>{{(bpConflict?.dependency?.productionProjectIds?.length) ? (bpConflict?.dependency?.productionProjectIds?.toString().replaceAll(',', ', ')) : '-'}}</span>
				</p>
				<p *ngIf="bpConflict?.dependency?.reportingProjectIds?.length">
					<span class="text-brand">ReportingProject: </span>
					<span>{{(bpConflict?.dependency?.reportingProjectIds?.length) ? (bpConflict?.dependency?.reportingProjectIds?.toString().replaceAll(',', ', ')) : '-'}}</span>
				</p>
				<p class="m-0" *ngIf="bpConflict?.dependency?.rbBaseProjectIds?.length">
					<span class="text-brand">RB Base Project: </span>
					<span>{{(bpConflict?.dependency?.rbBaseProjectIds?.length) ? (bpConflict?.dependency?.rbBaseProjectIds?.toString().replaceAll(',', ', ')) : '-'}}</span>
				</p>
                <p class="m-0" *ngIf="(bpConflict?.dependency !== null) && bpConflict?.dependency?.qcStatusPeriodId!==0">
					<span class="text-brand">* A QC Period is detected with status set to QC which prevents BP deletion: </span>
					<span>{{bpConflict?.dependency?.qcStatusPeriodId}}</span>
				</p>
				<p class="m-0" *ngIf="(bpConflict?.dependency === null) && bpConflict?.statusMsg?.includes('BaseProject Can\'t be deleted because it is part of a pending Retailer Separation Request')">
					<span class="text-brand">* BaseProject cannot be deleted due to a pending Retailer Separation Request. </span>
				</p>
			</div>
		</div>
	</div>
</gfk-modal>

<!-- QC Period Delete Modal -->
<gfk-modal
	[triggerModal]="deleteQCPeriodConfirmationModal"
	modalTitle="Confirm deletion of QC Period"
	cancelTitle="Cancel"
	confirmTitle="Yes, delete"
	(onAction)="deleteQCPeriod($event);">
	<p>This QC period will be deleted. Would you like to proceed?</p>
</gfk-modal>

<!-- QC Period Add / Edit Modal -->
<div class="qcPeriodFormModal">
    <gfk-modal
        width="1000px"
        [triggerModal]="qcPeriodFormModal"
        [modalTitle]="qcPeriodID ? 'Edit QC Period' : 'Add a new QC Period'"
        cancelTitle="Cancel"
        [confirmTitle]="userRole ? (qcPeriodID ? 'Save Changes' : 'Add QC Period') : ''"
        [confirmDisabled]="qcPeriodFormGroup?.invalid || disableQCPeriodEdit"
        (onAction)="saveQCPeriod($event);">
        <form [formGroup]="qcPeriodFormGroup" class="qc-period-form-modal-height" *ngIf="qcPeriodFormModal">
            <div class="last-update-time-text flex items-center qc-time-position" *ngIf="qcPeriodID">
                <span [gfk-tooltip]="qcPeriodCreatedOn" tooltipDirection="right" tooltipVariant="dark" class="mr-2" *ngIf="qcPeriodLastUpdateOn">
                    <eds-icon
                        eds-id="dx-qc-period-info"
                        size="md"
                        icon="info_outline"
                        color="inactive">
                    </eds-icon>
                </span>
                <span class="pb-1">
                    {{qcPeriodLastUpdateOn ? qcPeriodLastUpdateOn : qcPeriodCreatedOn}}
                </span>
            </div>
            <div>
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        QC Period to Load Data
                        <ng-container>*</ng-container>
                    </label>
                </div>
                <eds-select
                formControlName="periodId"
                eds-id="periodId"
                [options]="qcPeriodID ? periodList : currentPeriodList"
                [required]="true"
                [disabled]="false"
                [placeholder]="'Select to see options'"
                (edsChange)="updateReferencePeriods($event)"
                [value]="">
            </eds-select>
            </div>
            <div formArrayName="periods" [ngClass]="qcPeriodID ? 'pb-5' : ''">
                <div *ngFor="let period of periods.controls; let i = index" [formGroupName]="i">
                    <div class="flex">
                        <div class="mt-5 mr-5 w-1/2" [id]="'refProjectId-'+(i+1)">
                            <div class="mb-2">
                                <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                                    Reference Project {{i+1}}
                                    <ng-container *ngIf="i < 1 || period?.value?.id">*</ng-container>
                                </label>
                            </div>
                            <eds-select
                            formControlName="refProjectId"
                            eds-id="refProjectId"
                            [options]="baseProjectListForQCPeriod"
                            [placeholder]="'Select to see options'"
                            [disabled]="disableQCPeriodEdit ? true : false"
                            (edsChange)="onReferenceProjectSelected($event.detail, i); validateQCForm('referenceProject', i, period)"
                            [value]="">
                          </eds-select>
                          
                        </div>
                        <div class="mt-5 ml-5 w-1/2" [id]="'refPeriodId-'+(i+1)">
                            <div class="mb-2">
                                <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                                    Reference Period {{i+1}}
                                    <ng-container *ngIf="i < 1 || period?.value?.id">*</ng-container>
                                </label>
                            </div>
                            <eds-select
                            formControlName="refPeriodId"
                            eds-id="refPeriodId"
                            [options]="refPeriodLists[i] || []"
                            [disabled]="disableQCPeriodEdit ? true : false"
                            [placeholder]="'Select to see options'"
                            (edsChange)="validateQCForm('referencePeriod', i, period)"
                            [value]="">
                            </eds-select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5">
                <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                    Stocks Initialization
                </label>
            </div>
            <div class="flex pb-5">
                <div class="mt-5 mr-5 w-1/2">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Stock Base Project
                        </label>
                    </div>
                    <eds-select
                        formControlName="stockBaseProjectId"
                        eds-id="stockBaseProjectId"
                        [options]="baseProjectListForQCPeriod"
                        [required]="false"
                        [disabled]="disableQCPeriodEdit ? true : false"
                        [placeholder]="'Select to see options'"
                        (edsChange)="validateQCForm('stockProject')"
                        [value]="">
                    </eds-select>
                </div>
                <div class="mt-5 ml-5 w-1/2">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            Stock Period
                        </label>
                    </div>
                    <eds-select
                        formControlName="stockPeriodId"
                        eds-id="stockPeriodId"
                        [options]="qcPeriodID ? periodList : currentPeriodList"
                        [required]="false"
                        [disabled]="disableQCPeriodEdit ? true : false"
                        [placeholder]="'Select to see options'"
                        (edsChange)="validateQCForm('stockPeriod')"
                        [value]="">
                    </eds-select>
                </div>
            </div>
        </form>
    </gfk-modal>
</div>

<!-- Bulk QC Period Add Modal -->
<div class="qcPeriodFormModal">
    <gfk-modal
        [triggerModal]="createBulkQCPeriodModal"
        [modalTitle]="'Bulk create QC Period: Select a Target period'"
        cancelTitle="Cancel"
        [confirmTitle]="'Create QC Periods'"
        [confirmDisabled]="bulkQCPeriodFormGroup?.invalid"
        (onAction)="saveBulkQCPeriod($event);">
        <form [formGroup]="bulkQCPeriodFormGroup" class="qc-period-form-modal-height" *ngIf="createBulkQCPeriodModal">
            <div class="mb-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Target Period *
                    </label>
                    <eds-select
                        formControlName="startPeriod"
                        eds-id="startPeriod"
                        [options]="periodListForBulkQCPeriod"
                        [required]="true"
                        [disabled]="false"
                        [placeholder]="'Select to see options'"
                        [value]="">
                    </eds-select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Source Period
                    </label>
                    <div class="text-gray-600 text-sm">
                        {{ getBulkQCEndPeriodText() }}
                    </div>
                </div>
            </div>
            <div class="mt-5">
                <p class="text-sm text-gray-600">If the period already exists, it won't be created.</p>
            </div>
        </form>
    </gfk-modal>
</div>

<gfk-modal
    [triggerModal]="bpValidationModal"
    modalTitle="Failed to create Base Project"
    confirmTitle="View BCR Conflict Details"
    (onAction)="closeBPValidationModal($event)">
    <div>
        <p>
            Different definitions of Base Channel Rearrangements detected. Please correct the BCR definitions and try again.
        </p>
        <p class="text-brand">
            Number of conflicts: {{bcrConflictsCount}}
        </p>
    </div>
</gfk-modal>


<!-- Add QC Security Users Modal -->
<gfk-modal
	[triggerModal]="addQCSecurityUsersModal"
	[modalTitle]="'Add QC Security Users'"
	cancelTitle="Cancel"
	[confirmTitle]="'Save'"
	[confirmDisabled]="disableQCSecurityUsersModalBtn"
	(onAction)="closeAddQCSecurityUsersModal($event);">
	<form [formGroup]="addQCSecurityUsersForm" *ngIf="addQCSecurityUsersModal">
        <div class="mb-5">
            <gfk-radio-buttons [options]="qcSecurityUsersradioBtnOptions" [isInline]="true" (onChange)="toggleQCSecurityUsersFields($event)">
            </gfk-radio-buttons>
        </div>
        <div class="mb-5" *ngIf="showSourceField">
            <eds-select
                formControlName="sourceBPID"
                eds-id="sourceBPID"
                [options]="baseProjectListForQCPeriod"
                [placeholder]="'Select the Source BP to copy users from'">
            </eds-select>
        </div>
    	<dx-chip-autocomplete
            *ngIf="!showSourceField"
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="userList"
			[id]="'user-input'"
			[placeholder]="'Type and select users from the list'"
			[label]="'Users'">
		</dx-chip-autocomplete>
        <div class="filters-align comma-separated-input-group" *ngIf="!showSourceField">
            <span>
                <input
				id="usernamesInput"
				gfkInput
				type="text"
				formControlName="commaSeparatedUsernames"
                class="gfk-text-input"
                placeholder="Provide comma Separated list of usernames"
				eds-id="comma-separated-usernames-input"/>   
            </span>             
		</div>
	</form>
</gfk-modal>

<!-- QC Security Users Delete Modal -->
<gfk-modal
	[triggerModal]="removeAssignedUsersConfirmationModal"
	modalTitle="Confirm removal of Assigned Users"
	cancelTitle="Cancel"
	confirmTitle="Yes, delete"
	(onAction)="removeAssignedUsers($event);">
	<p>Selected users will be removed. Would you like to proceed?</p>
</gfk-modal>

<!-- Invalid Usernames Modal -->
<gfk-modal
	[triggerModal]="showInvalidUsernamesModal"
	modalTitle="Invalid Usernames"
	cancelTitle=""
	confirmTitle="Okay"
	(onAction)="closeInvalidUsernamesModal()">
	<div>
		<p>The following usernames could not be added as they are invalid:</p>
		<ul class="list-style ml-5">
			<li *ngFor="let username of invalidUsernames">
				<span>{{username}}</span>
			</li>
		</ul>
	</div>
</gfk-modal>

<!-- Delete Users Validation Modal -->
<gfk-modal
	[triggerModal]="deleteUserValidationModal"
	modalTitle="Users removed successfully"
	cancelTitle=""
	confirmTitle="Okay"
	(onAction)="closeDeleteUserValidationModal()">
	<div>
        <p class="flex">
            <span>
                Following users cannot be removed.
            </span>
            <span [gfk-tooltip]="'(Account Manager role users cannot remove Master Role users)'" tooltipDirection="right" tooltipVariant="dark" class="ml-2">
                <eds-icon
                    eds-id="dx-bp-security-delete-info"
                    icon="info_outline"
                    color="default"
                    size="md" >
                </eds-icon>
            </span>
        </p>
        <ul class="list-style ml-5">
            <li *ngFor="let conflictUser of deleteConflictUser">
                <span>{{conflictUser?.fullName + ' - (' + conflictUser?.email + ')'}}</span>
            </li>
        </ul>
	</div>
</gfk-modal>

<gfk-modal
	[triggerModal]="UnsavedChangesModal"
	modalTitle="Unsaved Changes"
	cancelTitle="Stay on Page"
	confirmTitle="Discard Changes"
	(onAction)="handleUnsavedChanges($event)">
	<p>
    Are you sure you want to leave this page? If you do, any unsaved changes will be lost.
	</p>
</gfk-modal>

    <!-- QC Status BP Modal -->
<gfk-modal
    [triggerModal]="QCDeleteValidationModal"
    modalTitle="QC Delete Request Failed"
    cancelTitle=""
    confirmTitle="Close"
    (onAction)="closeQCDeleteValidationModal()">
    <div *ngIf="QCPeriodQCStatusIds?.length">
        <p>
            QC Period(s) having status set to QC were prevented from being deleted.
        </p>
        <p class="text-brand">
            QC Period ID: {{QCPeriodQCStatusIds}}
        </p>
    </div>
    <div *ngIf="!QCPeriodQCStatusIds?.length">
        <p>
            Selected QC Period has status set to QC, which prevents QC Period from being deleted.
        </p>
    </div>
</gfk-modal>
<!-- QC Status BP Modal -->

  <!-- QC Status BP Modal -->
  <gfk-modal
  [triggerModal]="QCEditValidationModal"
  modalTitle="QC Update Request Failed"
  cancelTitle=""
  confirmTitle="Close"
  (onAction)="closeQCEditValidationModal()">
  <div>
      <p>
        QC Period cannot be edited as it is currently in QC Status.
      </p>
  </div>
  </gfk-modal>
  <!-- QC Status BP Modal -->

  <gfk-modal
  [triggerModal]="pgEditValidationModal"
  modalTitle="Failed to edit Base Project"
  cancelTitle=""
  confirmTitle="Ok"
  (onAction)="closepgEditValidationModal()">
  
  <div>
    <p>
      Following Product Group(s) cannot be removed due to dependencies or linkages to other projects:
    </p>

    <div class="bp-conflict-detail-height">
      <div *ngFor="let bpConflict of pgEditErrorMessage?.issues" class="bp-conflict-detail-card">
        
        <p class="flex mb-0">
          <span class="mr-2">
            <eds-icon
              eds-id="dx-qc-settings-info"
              icon="info"
              color="inactive"
              size="sm">
            </eds-icon>
          </span>
          <span class="text-lighter">
            PG ID: {{ bpConflict.productGroupId }} - {{ bpConflict.productGroupDesc }}
          </span>
        </p>

        <p *ngIf="bpConflict.hasDataLoaded">
          <span class="text-brand">Data Loaded: </span>
          <span>Yes</span>
        </p>

        <p *ngIf="bpConflict.rbProjectIds?.length">
          <span class="text-brand">RBBP IDs: </span>
          <span>{{ bpConflict.rbProjectIds.join(', ') }}</span>
        </p>

      </div>
    </div>
  </div>
</gfk-modal>

<gfk-modal
  [triggerModal]="pgEditQCPeriodValidationModal"
  modalTitle="Warning"
  cancelTitle="Cancel"
  confirmTitle="Update BP"
  (onAction)="closepgEditQCPeriodValidationModal($event)">
  
  <div>
    <p>
      Removing a PG will reset all QC Periods.
    </p>
  </div>
</gfk-modal>
<!-- Restore Confirmation Modal -->
<gfk-modal
	[triggerModal]="restoreConfirmationModal"
	modalTitle="Confirm restoration of Base Project"
	cancelTitle="Cancel"
	confirmTitle="Yes, restore"
	(onAction)="updateBaseProject()">
	<p>
		This base project will be restored. Would you like to proceed?
	</p>
</gfk-modal>


<gfk-modal
	[triggerModal]="cancelFormChangesModal"
	modalTitle="Unsaved Changes"
	cancelTitle="Cancel"
	confirmTitle="Discard Changes"
	(onAction)="selectedTab === 'qcProjectSettings' ? resetQCForm($event) : resetBPForm($event)">
	<p>
        Are you sure you want to reset your changes? If you do, any unsaved changes will be lost.
	</p>
</gfk-modal>

<div [hidden]="selectedTab !== 'bpAssociations'">
    <div class="mx-6">
        <article class="table-grid mt-6" *ngIf="showAssociationsTable">
            <div class="filter-list flex mb-6">
                <div>
                    <div class="toggle-button">
                        <div>
                            <div class="mb-2">
                                <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                                    Project Type
                                    <ng-container>*</ng-container>
                                </label>
                            </div>
                            <eds-select
                                eds-id="baseProjectAssociationType"
                                (edsChange)="updateCurrentProject($event.detail)"
                                [options]="associationsOptions"
                                [required]="true"
                                [placeholder]="'Start typing to see the Associations type'"
                                [value]="currentProjectType">
                            </eds-select>
                        </div>
                    </div>
                </div>
            </div>
            <eds-table eds-id="qc-period-table" [isLoading]="loadQCSecurityTableData" (edsRowSelected)="onSelect($event, assignedUsersData)">
                <thead slot="thead">
                    <eds-tr variant="header">
                        <eds-th [columnIndex]="0">ID</eds-th>
                        <eds-th column-index="2">Name</eds-th>
                        <eds-th column-index="3" *ngIf="currentProjectType==='RBBaseProject'">
                            <span class="pl-5">PanelType</span>
                        </eds-th>
                        <eds-th column-index="4">Changed By</eds-th>
                        <eds-th column-index="5">Changed On</eds-th>
                    </eds-tr>
                </thead>

                <tbody slot="tbody">
                    <ng-container *ngIf="currentProjects?.length > 0; else noData">
                        <eds-tr *ngFor="let currentProject of visibleCurrentProjects; trackBy: trackById" [rowIndex]="currentProject.projectId">
                            <eds-td>{{ currentProject.projectId }}</eds-td>
                            <eds-td>{{ currentProject.projectName }}</eds-td>
                            <eds-td *ngIf="currentProjectType==='RBBaseProject'"><span class="pl-5">{{ currentProject.panelTypeSdesc }}</span></eds-td>
                            <eds-td>{{ currentProject.changedBy }}</eds-td>
                            <eds-td>{{ currentProject.changedWhen }}</eds-td>
                        </eds-tr>
                    </ng-container>
                    <ng-template #noData>
                        <eds-tr><eds-td colspan="6">No records found.</eds-td></eds-tr>
                    </ng-template>
                </tbody>
            </eds-table>
            <gfk-pagination
                *ngIf="currentProjects?.length > 0 && currentProjects?.length > defaultPageSize"
                id="pagination-create-ld"
                [itemsPerPageOptions]="pageSizeOptions"
                [totalCount]="currentProjects?.length"
                [position]="'right'"
                [showItemsPerPage]="true"
                [showFirstAndLast]="true"
                [defaultPageSize]="defaultPageSize"
                [currentPage]="currentPageForAssociatedProjects"
                (onPage)="onPageChangeAssociations($event)">
            </gfk-pagination>
        </article>
        <div class="mt-5 flex items-center" *ngIf="!showAssociationsTable">
            <eds-icon
                eds-id="dx-qc-settings-info"
                icon="info_outline"
                color="default"
                size="md" >
            </eds-icon>
            <p class="pl-2 mb-0">There are no Associated Projects for this Base Project.</p>
        </div>
    </div>
</div>